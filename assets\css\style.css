/**
* Template Name: Personal - v4.7.0
* Template URL: https://bootstrapmade.com/personal-free-resume-bootstrap-template/
* Author: BootstrapMade.com/
* License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# General
--------------------------------------------------------------*/
body {
  font-family: "Open Sans", sans-serif;
  background-color: #040404;
  color: #fff;
  position: relative;
  background: transparent;
}
body::before {
  content: "";
  position: fixed;
  background: #040404 url("../img/bg.jpg") top right no-repeat;
  background-size: cover;
  left: 0;
  right: 0;
  top: 0;
  height: 100vh;
  z-index: -1;
}
@media (min-width: 1024px) {
  body::before {
    background-attachment: fixed;
  }
}

a {
  color: #3498DB;
  text-decoration: none;
}

a:hover {
  color: #53ace7;
  text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
  font-family: "Raleway", sans-serif;
}

/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/
#header {
  transition: ease-in-out 0.3s;
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  z-index: 997;
  overflow-y: auto;
}
#header * {
  transition: ease-in-out 0.3s;
}
#header h1 {
  font-size: 48px;
  margin: 0;
  padding: 0;
  line-height: 1;
  font-weight: 700;
  font-family: "Poppins", sans-serif;
}
#header h1 a, #header h1 a:hover {
  color: #fff;
  line-height: 1;
  display: inline-block;
}

#header h2 {
  font-size: 24px;
  margin-top: 32px; /* Increased spacing for better hierarchy */
  margin-bottom: 8px; /* Added bottom margin for better spacing */
  color: rgba(255, 255, 255, 0.9); /* Improved contrast */
  line-height: 1.4; /* Better line height for readability */
}
#header h2 span {
  color: #fff;
  border-bottom: 2px solid #3498DB;
  padding-bottom: 6px;
}
#header img {
  padding: 0;
  margin: 0;
}
#header .social-links {
  margin-top: 48px; /* Increased spacing for better breathing room */
  display: flex;
  gap: 16px; /* Modern gap property for consistent spacing */
  justify-content: center; /* Center align social links */
}
#header .social-links a {
  font-size: 18px; /* Slightly larger icons */
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  line-height: 1;
  border-radius: 50%;
  width: 48px; /* Larger touch targets */
  height: 48px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth transitions */
  backdrop-filter: blur(10px); /* Modern glass effect */
  border: 1px solid rgba(255, 255, 255, 0.2); /* Subtle border */
}
#header .social-links a:hover {
  background: #3498DB;
  transform: translateY(-3px) scale(1.05); /* Modern lift and scale effect */
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4); /* Glowing shadow */
  border-color: rgba(255, 255, 255, 0.4); /* Enhanced border on hover */
}
@media (max-width: 750px) {
  #header h1 {
    font-size: 200%;
  }
  #header h2 {
    font-size: 20px;
    line-height: 30px;
    margin-top: 24px; /* Increased spacing for mobile */
  }
  #header .social-links {
    margin-top: 32px; /* Increased spacing for mobile */
    gap: 12px; /* Reduced gap for mobile */
  }
  #header .social-links a {
    width: 44px; /* Slightly smaller for mobile */
    height: 44px;
    font-size: 16px;
  }
  #header .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 20px; /* Added horizontal padding */
  }
}
@media (max-width: 450px) {
  #header h1 {
    font-size: 170%;
  }
  #header h2 {
    font-size: 20px;
    line-height: 30px;
  }
  #header .social-links {
    margin-top: 15px;
  }
  #header .container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
/* Header Top */
#header.header-top {
  height: 80px;
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.85); /* Slightly more transparent */
  backdrop-filter: blur(20px); /* Strong backdrop blur for modern glass effect */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
  z-index: 1000; /* Ensure it stays on top */
}
#header.header-top .social-links, #header.header-top h2 {
  display: none;
}
#header.header-top h1 {
  margin-right: auto;
  font-size: 36px;
}
#header.header-top .container {
  display: flex;
  align-items: center;
  padding: 0 32px; /* Increased horizontal padding for better spacing */
}
#header.header-top .navbar {
  margin: 0;
}
@media (max-width: 768px) {
  #header.header-top {
    height: 60px;
  }
  #header.header-top h1 {
    font-size: 26px;
  }
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/**
* Desktop Navigation 
*/
.navbar {
  padding: 0;
  margin-top: 35px;
}
.navbar ul {
  margin: 0;
  padding: 0;
  display: flex;
  list-style: none;
  align-items: center;
}
.navbar li {
  position: relative;
}
.navbar li + li {
  margin-left: 48px; /* Increased from 30px for generous spacing (space-x-6 equivalent) */
}
.navbar a, .navbar a:focus {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px; /* Added generous padding for better touch targets */
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 500; /* Increased font weight for better readability */
  color: rgba(255, 255, 255, 0.8); /* Improved contrast */
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother transition */
  border-radius: 8px; /* Modern rounded corners */
  position: relative;
}
.navbar a i, .navbar a:focus i {
  font-size: 12px;
  line-height: 0;
  margin-left: 5px;
}
.navbar a:before {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background-color: #3498DB;
  visibility: hidden;
  width: 0px;
  transition: all 0.3s ease-in-out 0s;
}
.navbar a:hover:before, .navbar li:hover > a:before, .navbar .active:before {
  visibility: visible;
  width: 25px;
}
.navbar a:hover, .navbar .active, .navbar .active:focus, .navbar li:hover > a {
  color: #fff;
  background: rgba(255, 255, 255, 0.1); /* Modern hover background */
  backdrop-filter: blur(10px); /* Backdrop blur effect */
  transform: translateY(-2px); /* Subtle lift effect */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Modern shadow */
}

/**
* Mobile Navigation 
*/
.mobile-nav-toggle {
  color: #fff;
  font-size: 28px;
  cursor: pointer;
  display: none;
  line-height: 0;
  transition: 0.5s;
  position: fixed;
  right: 15px;
  top: 15px;
}

@media (max-width: 991px) {
  .mobile-nav-toggle {
    display: block;
  }

  .navbar ul {
    display: none;
  }
}
.navbar-mobile {
  position: fixed;
  overflow: hidden;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  transition: 0.3s;
  z-index: 999;
  margin-top: 0;
}
.navbar-mobile .mobile-nav-toggle {
  position: absolute;
  top: 15px;
  right: 15px;
}
.navbar-mobile ul {
  display: block;
  position: absolute;
  top: 55px;
  right: 15px;
  bottom: 45px;
  left: 15px;
  padding: 10px 0;
  overflow-y: auto;
  transition: 0.3s;
  border: 2px solid rgba(255, 255, 255, 0.2);
}
.navbar-mobile li {
  padding: 16px 24px; /* Increased padding for better touch targets */
}
.navbar-mobile li + li {
  margin: 0;
}
.navbar-mobile a, .navbar-mobile a:focus {
  font-size: 18px; /* Increased font size for better readability */
  position: relative;
  padding: 8px 12px; /* Added padding for better touch targets */
  border-radius: 8px; /* Modern rounded corners */
  transition: all 0.3s ease; /* Smooth transitions */
}

.navbar-mobile a:hover {
  background: rgba(255, 255, 255, 0.1); /* Hover background */
  color: #fff; /* Ensure text is visible */
}

/*--------------------------------------------------------------
# Sections General
--------------------------------------------------------------*/
section {
  overflow: hidden;
  position: absolute;
  width: 100%;
  top: 140px;
  bottom: 100%;
  opacity: 0;
  transition: ease-in-out 0.4s;
  z-index: 2;
}
section.section-show {
  top: 100px;
  bottom: auto;
  opacity: 1;
  padding-bottom: 45px;
}
section .container {
  background: rgba(0, 0, 0, 0.85); /* Slightly more transparent for modern look */
  backdrop-filter: blur(20px); /* Modern glass-morphism effect */
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
  border-radius: 16px; /* Modern rounded corners */
  padding: 48px; /* Increased padding for better breathing room */
  margin: 24px auto; /* Added margin for spacing between sections */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); /* Modern shadow */
}
@media (max-width: 768px) {
  section {
    top: 120px;
  }
  section.section-show {
    top: 80px;
  }
  section .container {
    padding: 32px 24px; /* Reduced padding for mobile but still generous */
    margin: 16px auto; /* Reduced margin for mobile */
  }
}

.section-title h2 {
  font-size: 14px;
  font-weight: 500;
  padding: 0;
  line-height: 1.2; /* Improved line height */
  margin: 0 0 32px 0; /* Increased bottom margin for better spacing */
  letter-spacing: 2px;
  text-transform: uppercase;
  color: #aaaaaa;
  font-family: "Poppins", sans-serif;
}
.section-title h2::after {
  content: "";
  width: 120px;
  height: 1px;
  display: inline-block;
  background: #3498DB;
  margin: 4px 10px;
}
.section-title p {
  margin: 0;
  margin: -15px 0 40px 0; /* Increased bottom margin for better spacing */
  font-size: 36px;
  font-weight: 700;
  text-transform: uppercase;
  font-family: "Poppins", sans-serif;
  color: #fff;
  line-height: 1.2; /* Better line height */
}

/*--------------------------------------------------------------
# About
--------------------------------------------------------------*/
.about-me .content h3 {
  font-weight: 700;
  font-size: 26px;
  color: #3498DB;
}
.about-me .content ul {
  list-style: none;
  padding: 0;
}
.about-me .content ul li {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}
.about-me .content ul strong {
  margin-right: 10px;
}
.about-me .content ul i {
  font-size: 16px;
  margin-right: 5px;
  color: #3498DB;
  line-height: 0;
}
.about-me .content p:last-child {
  margin-bottom: 0;
}

/*--------------------------------------------------------------
# Counts
--------------------------------------------------------------*/
.counts {
  padding: 70px 0 60px;
}
.counts .count-box {
  padding: 30px 30px 25px 30px;
  width: 100%;
  position: relative;
  text-align: center;
  background: rgba(255, 255, 255, 0.08);
}
.counts .count-box i {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24px;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px;
  color: #3498DB;
  border-radius: 50px;
  line-height: 0;
}
.counts .count-box span {
  font-size: 36px;
  display: block;
  font-weight: 600;
  color: #fff;
}
.counts .count-box p {
  padding: 0;
  margin: 0;
  font-family: "Raleway", sans-serif;
  font-size: 14px;
}

/*--------------------------------------------------------------
# Skills
--------------------------------------------------------------*/
.skills .progress {
  height: 60px;
  display: block;
  background: none;
  border-radius: 0;
}
.skills .progress .skill {
  padding: 10px 0;
  margin: 0;
  text-transform: uppercase;
  display: block;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  color: #fff;
}
.skills .progress .skill .val {
  float: right;
  font-style: normal;
}
.skills .progress-bar-wrap {
  background: rgba(255, 255, 255, 0.2);
}
.skills .progress-bar {
  width: 1px;
  height: 10px;
  transition: 0.9s;
  background-color: #3498DB;
}

/*--------------------------------------------------------------
# Interests
--------------------------------------------------------------*/
.interests .icon-box {
  display: flex;
  align-items: center;
  padding: 32px 24px; /* Increased padding for better breathing room */
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px); /* Modern glass effect */
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
  border-radius: 12px; /* Modern rounded corners */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother transition */
  margin-bottom: 16px; /* Added margin for spacing between items */
}
.interests .icon-box i {
  font-size: 36px; /* Slightly larger icons */
  padding-right: 20px; /* Increased spacing between icon and text */
  line-height: 1;
  min-width: 56px; /* Ensure consistent icon spacing */
}
.interests .icon-box h3 {
  font-weight: 600; /* Slightly reduced weight for better readability */
  margin: 0;
  padding: 0;
  line-height: 1.3; /* Better line height */
  font-size: 18px; /* Increased font size for better readability */
  color: #fff;
  font-family: "Poppins", sans-serif; /* Consistent font family */
}
.interests .icon-box:hover {
  background: rgba(255, 255, 255, 0.15); /* Enhanced hover background */
  transform: translateY(-4px); /* Modern lift effect */
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
  border-color: rgba(255, 255, 255, 0.2); /* Enhanced border on hover */
}

/*--------------------------------------------------------------
# Testimonials
--------------------------------------------------------------*/
.testimonials .testimonials-carousel, .testimonials .testimonials-slider {
  overflow: hidden;
}
.testimonials .testimonial-item {
  box-sizing: content-box;
  min-height: 320px;
}
.testimonials .testimonial-item .testimonial-img {
  width: 90px;
  border-radius: 50%;
  margin: -40px 0 0 40px;
  position: relative;
  z-index: 2;
  border: 6px solid rgba(255, 255, 255, 0.12);
}
.testimonials .testimonial-item h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 10px 0 5px 45px;
  color: #fff;
}
.testimonials .testimonial-item h4 {
  font-size: 14px;
  color: #999;
  margin: 0 0 0 45px;
}
.testimonials .testimonial-item .quote-icon-left, .testimonials .testimonial-item .quote-icon-right {
  color: rgba(255, 255, 255, 0.25);
  font-size: 26px;
}
.testimonials .testimonial-item .quote-icon-left {
  display: inline-block;
  left: -5px;
  position: relative;
}
.testimonials .testimonial-item .quote-icon-right {
  display: inline-block;
  right: -5px;
  position: relative;
  top: 10px;
}
.testimonials .testimonial-item p {
  font-style: italic;
  margin: 0 15px 0 15px;
  padding: 20px 20px 60px 20px;
  background: rgba(255, 255, 255, 0.1);
  position: relative;
  border-radius: 6px;
  position: relative;
  z-index: 1;
}
.testimonials .swiper-pagination {
  margin-top: 20px;
  position: relative;
}
.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.3);
}
.testimonials .swiper-pagination .swiper-pagination-bullet-active {
  background-color: #3498DB;
}

/*--------------------------------------------------------------
# Resume
--------------------------------------------------------------*/
.resume .resume-title {
  font-size: 26px;
  font-weight: 700;
  margin-top: 20px;
  margin-bottom: 20px;
  color: #fff;
}
.resume .resume-item {
  padding: 0 0 20px 20px;
  margin-top: -2px;
  border-left: 2px solid rgba(255, 255, 255, 0.2);
  position: relative;
}
.resume .resume-item h4 {
  line-height: 18px;
  font-size: 18px;
  font-weight: 600;
  text-transform: uppercase;
  font-family: "Poppins", sans-serif;
  color: #3498DB;
  margin-bottom: 10px;
}
.resume .resume-item h5 {
  font-size: 16px;
  background: rgba(255, 255, 255, 0.15);
  padding: 5px 15px;
  display: inline-block;
  font-weight: 600;
  margin-bottom: 10px;
}
.resume .resume-item ul {
  padding-left: 20px;
}
.resume .resume-item ul li {
  padding-bottom: 10px;
}
.resume .resume-item:last-child {
  padding-bottom: 0;
}
.resume .resume-item::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50px;
  left: -9px;
  top: 0;
  background: #3498DB;
  border: 2px solid #3498DB;
}

/*--------------------------------------------------------------
# Services
--------------------------------------------------------------*/
.services .icon-box {
  text-align: center;
  background: rgba(204, 204, 204, 0.1);
  padding: 80px 20px;
  transition: all ease-in-out 0.3s;
}
.services .icon-box .icon {
  margin: 0 auto;
  width: 64px;
  height: 64px;
  background: #3498DB;
  border-radius: 5px;
  transition: all 0.3s ease-out 0s;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  transform-style: preserve-3d;
}
.services .icon-box .icon i {
  color: #fff;
  font-size: 28px;
}
.services .icon-box .icon::before {
  position: absolute;
  content: "";
  left: -8px;
  top: -8px;
  height: 100%;
  width: 100%;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 5px;
  transition: all 0.3s ease-out 0s;
  transform: translateZ(-1px);
}
.services .icon-box h4 {
  font-weight: 700;
  margin-bottom: 15px;
  font-size: 24px;
}
.services .icon-box h4 a {
  color: #fff;
}
.services .icon-box p {
  line-height: 24px;
  font-size: 14px;
  margin-bottom: 0;
}
.services .icon-box:hover {
  background: #3498DB;
  border-color: #3498DB;
}
.services .icon-box:hover .icon {
  background: #fff;
}
.services .icon-box:hover .icon i {
  color: #3498DB;
}
.services .icon-box:hover .icon::before {
  background: #35e888;
}
.services .icon-box:hover h4 a, .services .icon-box:hover p {
  color: #fff;
}

/*--------------------------------------------------------------
# Portfolio
--------------------------------------------------------------*/
.portfolio .portfolio-item {
  margin-bottom: 40px; /* Increased spacing between portfolio items */
}
.portfolio #portfolio-flters {
  padding: 0;
  margin: 0 auto 15px auto;
  list-style: none;
  text-align: center;
  border-radius: 50px;
  padding: 2px 15px;
}
.portfolio #portfolio-flters li {
  cursor: pointer;
  display: inline-block;
  padding: 8px 16px 10px 16px;
  font-size: 14px;
  font-weight: 600;
  line-height: 1;
  text-transform: uppercase;
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 3px 10px 3px;
  transition: all 0.3s ease-in-out;
  border-radius: 4px;
}
.portfolio #portfolio-flters li:hover, .portfolio #portfolio-flters li.filter-active {
  background: #3498DB;
}
.portfolio #portfolio-flters li:last-child {
  margin-right: 0;
}
.portfolio .portfolio-wrap {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother transition */
  position: relative;
  overflow: hidden;
  z-index: 1;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px; /* Modern rounded corners */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* Subtle shadow */
}
.portfolio .portfolio-wrap::before {
  content: "";
  background: rgba(0, 0, 0, 0.6);
  position: absolute;
  left: 30px;
  right: 30px;
  top: 30px;
  bottom: 30px;
  transition: all ease-in-out 0.3s;
  z-index: 2;
  opacity: 0;
}
.portfolio .portfolio-wrap .portfolio-info {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
  z-index: 3;
  transition: all ease-in-out 0.3s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.portfolio .portfolio-wrap .portfolio-info::before {
  display: block;
  content: "";
  width: 48px;
  height: 48px;
  position: absolute;
  top: 35px;
  left: 35px;
  border-top: 3px solid #fff;
  border-left: 3px solid #fff;
  transition: all 0.5s ease 0s;
  z-index: 9994;
}
.portfolio .portfolio-wrap .portfolio-info::after {
  display: block;
  content: "";
  width: 48px;
  height: 48px;
  position: absolute;
  bottom: 35px;
  right: 35px;
  border-bottom: 3px solid #fff;
  border-right: 3px solid #fff;
  transition: all 0.5s ease 0s;
  z-index: 9994;
}
.portfolio .portfolio-wrap .portfolio-info h4 {
  font-size: 20px;
  color: #fff;
  font-weight: 600;
}
.portfolio .portfolio-wrap .portfolio-info p {
  color: #ffffff;
  font-size: 14px;
  text-transform: uppercase;
  padding: 0;
  margin: 0;
}
.portfolio .portfolio-wrap .portfolio-links {
  text-align: center;
  z-index: 4;
}
.portfolio .portfolio-wrap .portfolio-links a {
  color: #fff;
  margin: 0 2px;
  font-size: 28px;
  display: inline-block;
  transition: 0.3s;
}
.portfolio .portfolio-wrap .portfolio-links a:hover {
  color: #63eda3;
}
.portfolio .portfolio-wrap:hover::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 1;
}
.portfolio .portfolio-wrap:hover .portfolio-info {
  opacity: 1;
}
.portfolio .portfolio-wrap:hover .portfolio-info::before {
  top: 15px;
  left: 15px;
}
.portfolio .portfolio-wrap:hover .portfolio-info::after {
  bottom: 15px;
  right: 15px;
}

/*--------------------------------------------------------------
# Portfolio Details
--------------------------------------------------------------*/
.portfolio-details {
  padding-top: 40px;
  background: rgba(0, 0, 0, 0.8);
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow-y: auto;
}
.portfolio-details .container {
  padding-top: 20px;
  padding-bottom: 40px;
}
.portfolio-details .portfolio-title {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 20px;
}
.portfolio-details .portfolio-info {
  padding-top: 45px;
}
.portfolio-details .portfolio-info h3 {
  font-size: 22px;
  font-weight: 400;
  margin-bottom: 20px;
}
.portfolio-details .portfolio-info ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}
.portfolio-details .portfolio-info ul li + li {
  margin-top: 10px;
}
.portfolio-details .portfolio-info p {
  font-size: 15px;
  padding: 15px 0 0 0;
}
@media (max-width: 992px) {
  .portfolio-details .portfolio-info {
    padding-top: 20px;
  }
}
.portfolio-details .swiper-pagination {
  margin-top: 20px;
  position: relative;
}
.portfolio-details .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.3);
}
.portfolio-details .swiper-pagination .swiper-pagination-bullet-active {
  background-color: #3498DB;
}

/*--------------------------------------------------------------
# Contact
--------------------------------------------------------------*/
.contact .info-box {
  color: #444444;
  padding: 32px 24px; /* Increased padding for better breathing room */
  width: 100%;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px); /* Modern glass effect */
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
  border-radius: 12px; /* Modern rounded corners */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth transitions */
  margin-bottom: 16px; /* Added spacing between contact boxes */
}
.contact .info-box i.bx {
  font-size: 28px; /* Slightly larger icons */
  color: #3498DB;
  border-radius: 50%;
  padding: 16px; /* Increased padding */
  float: left;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px); /* Glass effect */
  border: 1px solid rgba(255, 255, 255, 0.2); /* Subtle border */
  transition: all 0.3s ease; /* Smooth transitions */
}
.contact .info-box h3 {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 700;
  margin: 10px 0 8px 68px;
}
.contact .info-box p {
  padding: 0;
  color: #fff;
  line-height: 24px;
  font-size: 14px;
  margin: 0 0 0 68px;
}
.contact .info-box .social-links {
  margin: 5px 0 0 68px;
  display: flex;
}
.contact .info-box .social-links a {
  font-size: 18px;
  display: inline-block;
  color: #fff;
  line-height: 1;
  margin-right: 12px;
  transition: 0.3s;
}
.contact .info-box .social-links a:hover {
  color: #3498DB;
  transform: translateY(-2px); /* Subtle lift effect */
}

/* Add hover effect for contact info boxes */
.contact .info-box:hover {
  background: rgba(255, 255, 255, 0.12); /* Enhanced background on hover */
  transform: translateY(-4px); /* Modern lift effect */
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
  border-color: rgba(255, 255, 255, 0.2); /* Enhanced border on hover */
}

.contact .info-box:hover i.bx {
  transform: scale(1.1); /* Scale icon on hover */
  background: rgba(52, 152, 219, 0.2); /* Highlight background */
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
  .interests .icon-box {
    padding: 24px 20px; /* Reduced padding for mobile */
    margin-bottom: 12px; /* Reduced margin for mobile */
  }

  .interests .icon-box i {
    font-size: 32px; /* Slightly smaller icons for mobile */
    padding-right: 16px; /* Reduced spacing for mobile */
    min-width: 48px; /* Adjusted for mobile */
  }

  .interests .icon-box h3 {
    font-size: 16px; /* Adjusted font size for mobile */
  }

  .contact .info-box {
    padding: 24px 20px; /* Reduced padding for mobile */
    margin-bottom: 12px; /* Reduced margin for mobile */
  }

  .contact .info-box h3 {
    margin: 8px 0 6px 60px; /* Adjusted margins for mobile */
  }

  .contact .info-box p {
    margin: 0 0 0 60px; /* Adjusted margins for mobile */
  }

  .contact .info-box .social-links {
    margin: 4px 0 0 60px; /* Adjusted margins for mobile */
  }
}
.contact .php-email-form {
  padding: 30px;
  background: rgba(255, 255, 255, 0.08);
}
.contact .php-email-form .error-message {
  display: none;
  background: rgba(255, 255, 255, 0.08);
  background: #ed3c0d;
  text-align: left;
  padding: 15px;
  font-weight: 600;
}
.contact .php-email-form .error-message br + br {
  margin-top: 25px;
}
.contact .php-email-form .sent-message {
  display: none;
  background: rgba(255, 255, 255, 0.08);
  background: #3498DB;
  text-align: center;
  padding: 15px;
  font-weight: 600;
}
.contact .php-email-form .loading {
  display: none;
  background: rgba(255, 255, 255, 0.08);
  text-align: center;
  padding: 15px;
}
.contact .php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid #3498DB;
  border-top-color: #eee;
  -webkit-animation: animate-loading 1s linear infinite;
  animation: animate-loading 1s linear infinite;
}
.contact .php-email-form input, .contact .php-email-form textarea {
  border-radius: 0;
  box-shadow: none;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.08);
  border: 0;
  transition: 0.3s;
  color: #fff;
}
.contact .php-email-form input:focus, .contact .php-email-form textarea:focus {
  background-color: rgba(255, 255, 255, 0.11);
}
.contact .php-email-form input::-moz-placeholder, .contact .php-email-form textarea::-moz-placeholder {
  color: rgba(255, 255, 255, 0.3);
  opacity: 1;
}
.contact .php-email-form input::placeholder, .contact .php-email-form textarea::placeholder {
  color: rgba(255, 255, 255, 0.3);
  opacity: 1;
}
.contact .php-email-form input {
  padding: 10px 15px;
}
.contact .php-email-form textarea {
  padding: 12px 15px;
}
.contact .php-email-form button[type=submit] {
  background: #3498DB;
  border: 0;
  padding: 10px 30px;
  color: #fff;
  transition: 0.4s;
  border-radius: 4px;
}
.contact .php-email-form button[type=submit]:hover {
  background: #15bb62;
}
@-webkit-keyframes animate-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes animate-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Credits
--------------------------------------------------------------*/
.credits {
  position: fixed;
  right: 0;
  left: 0;
  bottom: 0;
  padding: 15px;
  text-align: right;
  font-size: 13px;
  color: #fff;
  z-index: 999999;
}
@media (max-width: 992px) {
  .credits {
    text-align: center;
    padding: 10px;
    background: rgba(0, 0, 0, 0.8);
  }
}
.credits a {
  color: #3498DB;
  transition: 0.3s;
}
.credits a:hover {
  color: #fff;
}

